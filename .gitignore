# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 依赖目录
node_modules/
bower_components/

# 构建输出
dist/
build/
*.min.js
*.min.css

# 临时文件
*.tmp
*.temp
.cache/

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 测试覆盖率
coverage/
.nyc_output/

# 其他
.sass-cache/
.parcel-cache/
