{"version": 2, "name": "xia<PERSON>ia<PERSON>", "builds": [{"src": "**/*", "use": "@vercel/static"}], "routes": [{"src": "/(.*)", "dest": "/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(css|js|jpg|jpeg|png|gif|ico|svg|mp3|mp4|woff|woff2))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}