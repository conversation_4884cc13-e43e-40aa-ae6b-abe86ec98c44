[build]
  # 发布目录
  publish = "."
  
  # 构建命令（静态网站不需要构建）
  command = ""

[build.environment]
  # Node.js 版本
  NODE_VERSION = "18"

# 重定向规则
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# 头部设置
[[headers]]
  for = "/*"
  [headers.values]
    # 安全头部
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # 缓存控制
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.mp3"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.jpeg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# 表单处理（如果需要）
# [build.processing]
#   skip_processing = false
# [build.processing.css]
#   bundle = true
#   minify = true
# [build.processing.js]
#   bundle = true
#   minify = true
# [build.processing.html]
#   pretty_urls = true
# [build.processing.images]
#   compress = true
