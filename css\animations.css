/* 动画效果样式文件 */

/* 关键帧动画定义 */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(0, 184, 148, 0.3);
    }
    100% {
        transform: scale(1.05);
        box-shadow: 0 0 20px rgba(0, 184, 148, 0.6);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
    100% {
        opacity: 0;
        transform: scale(0.8);
    }
}

@keyframes slideDown {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
    }
}

@keyframes scoreUp {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-40px) scale(1);
        opacity: 0;
    }
}

@keyframes comboEffect {
    0% {
        transform: scale(1) rotate(0deg);
        color: #2d3436;
    }
    50% {
        transform: scale(1.3) rotate(5deg);
        color: #ff6b6b;
    }
    100% {
        transform: scale(1) rotate(0deg);
        color: #2d3436;
    }
}

/* 动画类 */
.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

.bounce {
    animation: bounce 0.6s ease-in-out;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.glow {
    animation: glow 1s ease-in-out infinite;
}

.score-up {
    animation: scoreUp 1s ease-out forwards;
}

.combo-effect {
    animation: comboEffect 0.5s ease-in-out;
}

/* 游戏元素特殊动画 */
.game-cell.removing {
    animation: fadeOut 0.4s ease-out forwards;
    z-index: 10;
}

.game-cell.falling {
    animation: slideDown 0.3s ease-out;
}

.game-cell.new-element {
    animation: slideDown 0.4s ease-out, bounce 0.6s ease-in-out 0.4s;
}

.game-cell.invalid-move {
    animation: shake 0.5s ease-in-out;
}

.game-cell.combo-highlight {
    animation: glow 0.8s ease-in-out infinite;
}

/* 分数动画 */
.score-value.updating {
    animation: comboEffect 0.5s ease-in-out;
}

.score-popup {
    position: absolute;
    font-size: 1.5em;
    font-weight: bold;
    color: #ff6b6b;
    pointer-events: none;
    z-index: 100;
    animation: scoreUp 1s ease-out forwards;
}

/* 连击效果 */
.combo-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2em;
    font-weight: bold;
    color: #ff6b6b;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    pointer-events: none;
    z-index: 200;
    animation: comboEffect 1s ease-in-out;
}

/* 按钮动画 */
.control-btn.clicked {
    animation: bounce 0.3s ease-in-out;
}

/* 模态框动画 */
.modal.show .modal-content {
    animation: slideDown 0.3s ease-out, bounce 0.6s ease-in-out 0.3s;
}

/* 加载动画增强 */
.loading-spinner {
    animation: spin 1s linear infinite;
}

/* 游戏状态消息动画 */
.status-message.updating {
    animation: bounce 0.5s ease-in-out;
}

/* 特殊效果：彩虹边框 */
@keyframes rainbow {
    0% { border-color: #ff6b6b; }
    16.66% { border-color: #ffa726; }
    33.33% { border-color: #ffee58; }
    50% { border-color: #66bb6a; }
    66.66% { border-color: #42a5f5; }
    83.33% { border-color: #ab47bc; }
    100% { border-color: #ff6b6b; }
}

.game-cell.rainbow-effect {
    animation: rainbow 2s linear infinite;
    border-width: 3px;
}

/* 过渡效果 */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果增强 */
.game-cell:hover {
    transition: all 0.2s ease;
}

.control-btn:hover {
    transition: all 0.2s ease;
}

/* 禁用状态 */
.game-cell.disabled {
    pointer-events: none;
    opacity: 0.6;
    filter: grayscale(50%);
}

.control-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 成功提示动画 */
@keyframes success {
    0% {
        transform: scale(1);
        background-color: rgba(0, 184, 148, 0.1);
    }
    50% {
        transform: scale(1.05);
        background-color: rgba(0, 184, 148, 0.3);
    }
    100% {
        transform: scale(1);
        background-color: rgba(0, 184, 148, 0.1);
    }
}

.success-effect {
    animation: success 0.6s ease-in-out;
}
