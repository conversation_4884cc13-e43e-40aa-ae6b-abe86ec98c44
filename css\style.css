/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

/* 游戏容器 */
.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
    text-align: center;
}

/* 游戏标题 */
.game-header {
    margin-bottom: 20px;
}

.game-title {
    font-size: 2.5em;
    color: #ff6b6b;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-subtitle {
    color: #666;
    font-size: 1.1em;
}

/* 游戏信息栏 */
.game-info {
    background: linear-gradient(45deg, #ffeaa7, #fab1a0);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
}

.score-section {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.score-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.score-label {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.score-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #2d3436;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* 游戏棋盘容器 */
.game-board-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    padding: 10px;
}

/* 游戏棋盘 */
.game-board {
    display: grid;
    grid-template-columns: repeat(10, 50px);
    grid-template-rows: repeat(10, 50px);
    gap: 2px;
    background: #34495e;
    padding: 10px;
    border-radius: 10px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

/* 游戏元素 */
.game-cell {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: #ecf0f1;
    border: 2px solid transparent;
}

.game-cell img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.game-cell:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.game-cell.selected {
    border-color: #ff6b6b;
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.5);
}

.game-cell.highlight {
    border-color: #00b894;
    animation: pulse 0.5s infinite alternate;
}

/* 控制按钮区域 */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 100px;
}

.control-btn.primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.control-btn:active {
    transform: translateY(0);
}

.btn-icon {
    font-size: 1.2em;
}

/* 游戏状态提示 */
.game-status {
    background: rgba(116, 185, 255, 0.1);
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 10px;
}

.status-message {
    color: #0984e3;
    font-weight: bold;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-content h2 {
    color: #2d3436;
    margin-bottom: 15px;
}

.final-score {
    font-size: 1.5em;
    color: #ff6b6b;
    font-weight: bold;
    margin: 20px 0;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

/* 设置项 */
.setting-item {
    margin: 15px 0;
    text-align: left;
}

.setting-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.setting-item input,
.setting-item select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* 加载提示 */
.loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading.show {
    display: flex;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #ff6b6b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

/* 调试面板 */
.debug-panel {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-top: 10px;
    font-size: 0.9em;
}

.debug-panel h4 {
    margin-bottom: 10px;
    color: #ff6b6b;
}

.debug-panel p {
    margin: 5px 0;
}

.debug-panel button {
    margin: 5px;
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    background: #74b9ff;
    color: white;
    cursor: pointer;
}

.debug-panel button:hover {
    background: #0984e3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        margin: 10px;
        padding: 15px;
    }

    .game-board {
        grid-template-columns: repeat(10, 40px);
        grid-template-rows: repeat(10, 40px);
    }

    .game-cell {
        width: 40px;
        height: 40px;
    }

    .game-title {
        font-size: 2em;
    }

    .control-btn {
        min-width: 80px;
        padding: 8px 12px;
        font-size: 0.8em;
    }
}
